name: 构建与自动发布

on:
  push:
    branches:
      - main
    tags:
      - 'v*'

jobs:
  check-version:
    runs-on: ubuntu-latest
    outputs:
      should_release: ${{ steps.check.outputs.should_release }}
      version: ${{ steps.check.outputs.version }}
      
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: 检查版本号是否变更
        id: check
        run: |
          # 获取当前版本号
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          echo "当前版本: $CURRENT_VERSION"
          
          # 获取上一次提交的版本号
          git checkout HEAD~1 package.json || true
          PREVIOUS_VERSION=$(node -p "require('./package.json').version" || echo "0.0.0")
          echo "上一个版本: $PREVIOUS_VERSION"
          
          # 切回当前分支
          git checkout HEAD package.json
          
          # 比较版本号
          if [ "$CURRENT_VERSION" != "$PREVIOUS_VERSION" ]; then
            echo "版本号已更新: $PREVIOUS_VERSION -> $CURRENT_VERSION"
            echo "should_release=true" >> $GITHUB_OUTPUT
            echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          else
            echo "版本号未更改: $CURRENT_VERSION"
            echo "should_release=false" >> $GITHUB_OUTPUT
            echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          fi
        shell: bash

  build-and-release:
    needs: check-version
    if: needs.check-version.outputs.should_release == 'true'
    runs-on: ${{ matrix.os }}
    
    strategy:
      matrix:
        os: [windows-latest]
        # 未来可以添加: macos-latest, ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
      
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: 缓存依赖
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
      
      - name: 安装依赖
        run: npm ci
      
      - name: 构建应用
        run: npm run build
        env:
          CI: false
        
      - name: 获取版本号
        id: package_version
        run: echo "version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT
        shell: bash
      
      - name: 创建NSIS安装包
        run: npm run package
        env:
          CI: false
      
      - name: 清理临时文件并确认最终产物
        run: |
          Write-Host "=== 清理前的文件列表 ==="
          Get-ChildItem -Path release -Recurse -File | Select-Object FullName, @{Name="Size(MB)";Expression={[math]::Round($_.Length/1MB, 2)}} | Format-Table -AutoSize
          
          # 清理临时目录
          Write-Host "`n=== 清理临时目录 ==="
          if (Test-Path -Path "release\win-unpacked") {
            Remove-Item -Path "release\win-unpacked" -Recurse -Force
            Write-Host "已删除 win-unpacked 目录"
          }
          
          if (Test-Path -Path "release\.icon-ico") {
            Remove-Item -Path "release\.icon-ico" -Recurse -Force
            Write-Host "已删除 .icon-ico 目录"
          }
          
          # 重命名安装程序
          $version = "${{ steps.package_version.outputs.version }}"
          Write-Host "`n=== 重命名安装程序 ==="
          $setupFile = Get-ChildItem -Path "release" -Filter "*.exe" | Where-Object { $_.Name -like "TexStudio*" -or $_.Name -like "*-setup-*.exe" } | Select-Object -First 1
          if ($setupFile) {
            $newName = "LaTeX-latest.exe"
            $newPath = Join-Path -Path "release" -ChildPath $newName
            Rename-Item -Path $setupFile.FullName -NewName $newName -Force
            Write-Host "已将 $($setupFile.Name) 重命名为: $newName"
          } else {
            Write-Host "警告: 找不到安装程序文件!"
            Get-ChildItem -Path "release" -File | ForEach-Object { Write-Host " - $($_.Name)" }
          }
          
          # 修改latest.yml文件
          if (Test-Path -Path "release\latest.yml") {
            # 修改latest.yml文件内容，更新文件名
            $ymlContent = Get-Content -Path "release\latest.yml" -Raw
            $ymlContent = $ymlContent -replace "TexStudio-$version.exe", "LaTeX-latest.exe"
            $ymlContent = $ymlContent -replace "path: TexStudio-$version.exe", "path: LaTeX-latest.exe"
            Set-Content -Path "release\latest.yml" -Value $ymlContent
            
            Write-Host "已处理latest.yml文件"
          } else {
            Write-Host "警告: 找不到latest.yml文件!"
          }
          
          # 删除不需要的文件
          Write-Host "`n=== 删除多余文件 ==="
          Get-ChildItem -Path "release" -File | Where-Object { 
            $_.Name -ne "LaTeX-latest.exe" -and 
            $_.Name -ne "latest.yml"
          } | ForEach-Object {
            Remove-Item -Path $_.FullName -Force
            Write-Host "已删除文件: $($_.Name)"
          }
          
          Write-Host "`n=== 最终文件列表 ==="
          Get-ChildItem -Path release -File | Select-Object Name, @{Name="Size(MB)";Expression={[math]::Round($_.Length/1MB, 2)}} | Format-Table -AutoSize
        shell: pwsh
      
      - name: 创建 Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.package_version.outputs.version }}
          name: Release v${{ steps.package_version.outputs.version }}
          draft: false
          prerelease: false
          files: |
            ./release/LaTeX-latest.exe
            ./release/latest.yml
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - name: 上传构建产物
        uses: actions/upload-artifact@v4
        with:
          name: LaTeX-installer
          path: |
            release/LaTeX-latest.exe

  notify:
    needs: [check-version, build-and-release]
    if: always() && needs.check-version.outputs.should_release == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: 发布状态通知
        run: |
          if [ "${{ needs.build-and-release.result }}" == "success" ]; then
            echo "✅ 版本 ${{ needs.check-version.outputs.version }} 已成功发布!"
          else
            echo "❌ 版本 ${{ needs.check-version.outputs.version }} 发布失败!"
          fi
        shell: bash
