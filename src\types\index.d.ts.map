{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["index.ts"], "names": [], "mappings": "AACA,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;CACf;AAGD,MAAM,WAAW,iBAAiB;IAChC,MAAM,EAAE,OAAO,CAAC;IAChB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,GAAG,CAAC,EAAE;QACJ,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;CACH;AAGD,MAAM,WAAW,SAAS;IACxB,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;CACnB;AAGD,MAAM,WAAW,WAAW;IAC1B,SAAS,EAAE,SAAS,CAAC;IACrB,SAAS,EAAE,cAAc,CAAC;IAC1B,OAAO,EAAE,WAAW,EAAE,CAAC;CACxB;AAGD,MAAM,WAAW,cAAc;IAC7B,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;CAChB;AAGD,MAAM,MAAM,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;AAGvD,MAAM,WAAW,QAAQ;IACvB,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5B,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE,OAAO,CAAC;IACvB,aAAa,EAAE,MAAM,CAAC;IACtB,OAAO,EAAE,WAAW,EAAE,CAAC;CACxB;AAGD,MAAM,WAAW,cAAc;IAC7B,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;CAChB;AAGD,MAAM,WAAW,WAAW;IAE1B,UAAU,EAAE,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;IACzC,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;IAGlE,cAAc,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC;IAC1D,qBAAqB,EAAE,MAAM,IAAI,CAAC;IAGlC,eAAe,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;IAGxC,WAAW,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,CAAC;IACxC,YAAY,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAGhE,gBAAgB,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,KAAK,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAG1F,uBAAuB,EAAE,CAAC,SAAS,EAAE,cAAc,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACtE,yBAAyB,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAG/C,cAAc,EAAE,MAAM,IAAI,CAAC;IAC3B,WAAW,EAAE,MAAM,IAAI,CAAC;IAGxB,mBAAmB,EAAE,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,KAAK,IAAI,KAAK,IAAI,CAAC;IAChF,oBAAoB,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC;CACvE;AAGD,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,MAAM;QACd,WAAW,EAAE,WAAW,CAAC;KAC1B;CACF"}