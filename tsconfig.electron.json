{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "outDir": "dist/electron", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/electron/**/*", "src/types/**/*", "src/utils/**/*"], "exclude": ["node_modules", "dist", "build", "src/components", "src/App.tsx", "src/index.tsx"]}